'use client'

import {
  Settings as SettingsIcon,
  Store,
  User,
  Bell,
  Palette,
  Shield,
  Database,
  Save,
  RefreshCw,
  Moon,
  Sun,
  Monitor,
  Smartphone,
  Mail,
  MessageSquare,
  Lock,
  Key,
  Download,
  Upload,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect, useCallback } from 'react'

// Remove unused imports to fix TypeScript warnings
import { logger } from '@/lib/logger'
import type {
  StoreSettings,
  UserPreferences,
  SettingsSection
} from '@/types/settings'
import {
  DEFAULT_STORE_SETTINGS,
  DEFAULT_USER_PREFERENCES,
  SUPPORTED_CURRENCIES,
  SUPPORTED_TIMEZONES,
  REFRESH_INTERVALS
} from '@/types/settings'

// Types are now imported from @/types/settings

export default function Settings() {
  const { resolvedTheme } = useTheme()
  // Remove unused variables to fix TypeScript warnings
  const [mounted, setMounted] = useState(false)
  const [activeTab, setActiveTab] = useState('store')
  const [isLoading, setIsLoading] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [error, setError] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [saveError] = useState<string | null>(null)

  // Settings state
  const [storeSettings, setStoreSettings] = useState<StoreSettings>(DEFAULT_STORE_SETTINGS)
  const [userPreferences, setUserPreferences] = useState<UserPreferences>(DEFAULT_USER_PREFERENCES)

  // Move function declarations before they're used in useEffect dependencies
  const loadSettings = useCallback(async () => {
    try {
      logger.info('Loading settings')
      setIsLoading(true)

      // Try to load from database first (primary source)
      try {
        const response = await fetch('/api/settings', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (response.ok) {
          const data = await response.json()
          logger.info('Settings loaded from database', { hasStore: !!data.settings?.store, hasUser: !!data.settings?.user })

          if (data.settings?.store) {
            setStoreSettings(data.settings.store)
          }
          if (data.settings?.user) {
            setUserPreferences(data.settings.user)
          }

          setHasChanges(false)
          return
        } else {
          logger.warn('Database settings load failed, trying localStorage')
        }
      } catch {
        logger.info('Database error, falling back to localStorage')
      }

      // Fallback to localStorage
      const storedStoreSettings = localStorage.getItem('storeSettings')
      const storedUserPreferences = localStorage.getItem('userPreferences')

      if (storedStoreSettings) {
        try {
          const parsed = JSON.parse(storedStoreSettings)
          setStoreSettings(parsed)
          logger.info('Store settings loaded from localStorage')
        } catch (error) {
          logger.error('Failed to parse stored store settings', error)
        }
      }

      if (storedUserPreferences) {
        try {
          const parsed = JSON.parse(storedUserPreferences)
          setUserPreferences(parsed)
          logger.info('User preferences loaded from localStorage')
        } catch (error) {
          logger.error('Failed to parse stored user preferences', error)
        }
      }

      setHasChanges(false)
    } catch (error) {
      logger.error('Failed to load settings', error)
      setError('Failed to load settings. Using defaults.')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const saveSettings = useCallback(async () => {
    if (!validateStoreSettings()) {
      setError('Please fill in all required store information')
      return
    }

    try {
      setIsSaving(true)
      setError(null)
      logger.info('Saving settings')

      // Try to save to database first (primary storage)
      try {
        const response = await fetch('/api/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'both',
            storeSettings,
            userPreferences,
          }),
        })

        if (response.ok) {
          logger.info('Settings saved to database successfully')
          setHasChanges(false)
          setLastSaved(new Date())
          return
        } else {
          const errorData = await response.json()
          logger.warn('Database save failed, falling back to localStorage', { error: errorData })
        }
      } catch {
        logger.info('Database save failed, falling back to localStorage')
      }

      // Fallback to localStorage
      localStorage.setItem('storeSettings', JSON.stringify(storeSettings))
      localStorage.setItem('userPreferences', JSON.stringify(userPreferences))

      logger.info('Settings saved to localStorage')
      setHasChanges(false)
      setLastSaved(new Date())
    } catch (error) {
      logger.error('Failed to save settings', error)
      setError('Failed to save settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }, [storeSettings, userPreferences, validateStoreSettings])

  useEffect(() => {
    setMounted(true)
    // Load settings from localStorage or API
    loadSettings()
  }, [loadSettings])

  // Auto-save functionality with debouncing
  useEffect(() => {
    if (!hasChanges || !autoSaveEnabled || !mounted) return

    const autoSaveTimer = setTimeout(() => {
      logger.info('Auto-saving settings')
      saveSettings()
    }, 3000) // Auto-save after 3 seconds of inactivity

    return () => clearTimeout(autoSaveTimer)
  }, [hasChanges, storeSettings, userPreferences, autoSaveEnabled, mounted, saveSettings])

  // Prevent data loss on page unload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasChanges) {
        e.preventDefault()
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return e.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasChanges])

  // Duplicate loadSettings function removed - already declared above

  // loadFromLocalStorage function removed - not used

  const validateStoreSettings = useCallback((): boolean => {
    const errors: Record<string, string> = {}

    // Store name validation
    if (!storeSettings.storeName.trim()) {
      errors.storeName = 'Store name is required'
    } else if (storeSettings.storeName.trim().length < 2) {
      errors.storeName = 'Store name must be at least 2 characters'
    } else if (storeSettings.storeName.trim().length > 255) {
      errors.storeName = 'Store name must be less than 255 characters'
    }

    // Email validation
    if (storeSettings.storeEmail && storeSettings.storeEmail.trim()) {
      const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/
      if (!emailRegex.test(storeSettings.storeEmail.trim())) {
        errors.storeEmail = 'Please enter a valid email address'
      }
    }

    // Phone validation
    if (storeSettings.storePhone && storeSettings.storePhone.trim()) {
      const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,20}$/
      if (!phoneRegex.test(storeSettings.storePhone.trim())) {
        errors.storePhone = 'Please enter a valid phone number'
      }
    }

    // Store description validation
    if (storeSettings.storeDescription.trim().length > 1000) {
      errors.storeDescription = 'Store description must be less than 1000 characters'
    }

    // Store address validation
    if (storeSettings.storeAddress.trim().length > 500) {
      errors.storeAddress = 'Store address must be less than 500 characters'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }, [storeSettings])

  // Duplicate saveSettings function removed - already declared above

  const settingsSections: SettingsSection[] = [
    {
      id: 'store',
      label: 'Store Configuration',
      icon: Store,
      description: 'Manage your store information and business details'
    },
    {
      id: 'user',
      label: 'User Preferences',
      icon: User,
      description: 'Customize your personal settings and preferences'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      description: 'Configure notification settings and alerts'
    },
    {
      id: 'appearance',
      label: 'Appearance',
      icon: Palette,
      description: 'Customize theme and visual preferences'
    },
    {
      id: 'security',
      label: 'Security',
      icon: Shield,
      description: 'Manage security settings and access controls'
    },
    {
      id: 'system',
      label: 'System',
      icon: Database,
      description: 'System configuration and maintenance'
    }
  ]

  const handleStoreSettingChange = (field: keyof StoreSettings, value: string) => {
    setStoreSettings(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const handleUserPreferenceChange = (field: string, value: unknown) => {
    setUserPreferences(prev => {
      const newPrefs = { ...prev }
      const keys = field.split('.')
      let current: Record<string, unknown> = newPrefs as Record<string, unknown>

      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i]
        if (!key) continue // Skip empty keys

        if (typeof current[key] === 'object' && current[key] !== null) {
          current = current[key] as Record<string, unknown>
        } else {
          // Create nested object if it doesn't exist
          current[key] = {}
          current = current[key] as Record<string, unknown>
        }
      }

      const lastKey = keys[keys.length - 1]
      if (lastKey) {
        current[lastKey] = value
      }

      return newPrefs
    })
    setHasChanges(true)
  }

  if (!mounted) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-green-500" />
      </div>
    )
  }

  return (
    <div
      className="p-6 space-y-6"
      style={{
        backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb',
        minHeight: 'calc(100vh - 4rem)'
      }}
    >
      {/* Header */}
      <div
        className="rounded-2xl shadow-lg p-6 border"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb',
          background: resolvedTheme === 'dark'
            ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div
              className="p-3 rounded-xl"
              style={{
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'
              }}
            >
              <SettingsIcon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1
                className="text-2xl font-bold"
                style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
              >
                Settings
              </h1>
              <div className="flex items-center gap-3">
                <p
                  className="text-sm"
                  style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}
                >
                  Configure your store settings and preferences
                </p>
                {/* Status Indicator */}
                <div className="flex items-center gap-2">
                  {isSaving ? (
                    <div className="flex items-center gap-1">
                      <RefreshCw className="h-3 w-3 animate-spin text-blue-500" />
                      <span className="text-xs text-blue-500">Saving...</span>
                    </div>
                  ) : lastSaved ? (
                    <div className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span className="text-xs text-green-500">
                        Saved {lastSaved.toLocaleTimeString()}
                      </span>
                    </div>
                  ) : hasChanges ? (
                    <div className="flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3 text-yellow-500" />
                      <span className="text-xs text-yellow-500">Unsaved changes</span>
                    </div>
                  ) : null}

                  {error && (
                    <div className="flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3 text-red-500" />
                      <span className="text-xs text-red-500" title={error}>
                        Error
                      </span>
                    </div>
                  )}

                  {saveError && (
                    <div className="flex items-center gap-1">
                      <Info className="h-3 w-3 text-orange-500" />
                      <span className="text-xs text-orange-500" title={saveError}>
                        Local only
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Auto-save Toggle */}
            <div className="flex items-center gap-2">
              <span
                className="text-sm"
                style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}
              >
                Auto-save
              </span>
              <button
                onClick={() => setAutoSaveEnabled(!autoSaveEnabled)}
                className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                  autoSaveEnabled ? 'bg-green-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                    autoSaveEnabled ? 'translate-x-5' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Manual Save Button */}
            {hasChanges && (
              <button
                onClick={saveSettings}
                disabled={isLoading}
                className="flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                style={{
                  backgroundColor: '#22c55e',
                  color: '#ffffff',
                  boxShadow: '0 4px 12px rgba(34, 197, 94, 0.3)'
                }}
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {isLoading ? 'Saving...' : 'Save Now'}
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div
          className="lg:col-span-1 rounded-2xl shadow-lg p-4 border h-fit"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
            borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
          }}
        >
          <nav className="space-y-1">
            {settingsSections.map((section) => {
              const Icon = section.icon
              const isActive = activeTab === section.id

              return (
                <button
                  key={section.id}
                  onClick={() => setActiveTab(section.id)}
                  className={`w-full flex items-center text-left p-3 rounded-xl transition-all duration-200 group ${
                    isActive ? 'shadow-md' : 'hover:shadow-sm'
                  }`}
                  style={{
                    background: isActive
                      ? (resolvedTheme === 'dark'
                        ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)'
                        : 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)')
                      : 'transparent',
                    border: isActive
                      ? (resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.4)' : '1px solid rgba(34, 197, 94, 0.3)')
                      : '1px solid transparent'
                  }}
                >
                  <div
                    className="p-2 rounded-lg mr-3"
                    style={{
                      background: isActive
                        ? (resolvedTheme === 'dark'
                          ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)'
                          : 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)')
                        : (resolvedTheme === 'dark'
                          ? 'rgba(71, 85, 105, 0.5)'
                          : 'rgba(243, 244, 246, 0.9)')
                    }}
                  >
                    <Icon
                      className="h-4 w-4"
                      style={{
                        color: isActive
                          ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                          : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b')
                      }}
                    />
                  </div>
                  <div>
                    <div
                      className="font-medium text-sm"
                      style={{
                        color: isActive
                          ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                          : (resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937')
                      }}
                    >
                      {section.label}
                    </div>
                    <div
                      className="text-xs"
                      style={{
                        color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'
                      }}
                    >
                      {section.description}
                    </div>
                  </div>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          {activeTab === 'store' && (
            <div
              className="rounded-2xl shadow-lg p-6 border space-y-6"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
              }}
            >
              <div className="flex items-center gap-3 mb-6">
                <Store className="h-6 w-6 text-blue-500" />
                <h3
                  className="text-xl font-bold"
                  style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                >
                  Store Configuration
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Store Name *
                  </label>
                  <input
                    type="text"
                    value={storeSettings.storeName}
                    onChange={(e) => handleStoreSettingChange('storeName', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:ring-2 focus:border-transparent ${
                      validationErrors.storeName
                        ? 'focus:ring-red-500 border-red-300'
                        : 'focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: validationErrors.storeName
                        ? '#ef4444'
                        : (resolvedTheme === 'dark' ? '#475569' : '#d1d5db'),
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                    placeholder="Enter your store name"
                  />
                  {validationErrors.storeName && (
                    <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      {validationErrors.storeName}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Store Phone
                  </label>
                  <input
                    type="tel"
                    value={storeSettings.storePhone}
                    onChange={(e) => handleStoreSettingChange('storePhone', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:ring-2 focus:border-transparent ${
                      validationErrors.storePhone
                        ? 'focus:ring-red-500 border-red-300'
                        : 'focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: validationErrors.storePhone
                        ? '#ef4444'
                        : (resolvedTheme === 'dark' ? '#475569' : '#d1d5db'),
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                    placeholder="+63 XXX XXX XXXX"
                  />
                  {validationErrors.storePhone && (
                    <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      {validationErrors.storePhone}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Store Email
                  </label>
                  <input
                    type="email"
                    value={storeSettings.storeEmail}
                    onChange={(e) => handleStoreSettingChange('storeEmail', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:ring-2 focus:border-transparent ${
                      validationErrors.storeEmail
                        ? 'focus:ring-red-500 border-red-300'
                        : 'focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: validationErrors.storeEmail
                        ? '#ef4444'
                        : (resolvedTheme === 'dark' ? '#475569' : '#d1d5db'),
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                    placeholder="<EMAIL>"
                  />
                  {validationErrors.storeEmail && (
                    <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      {validationErrors.storeEmail}
                    </p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Store Description
                  </label>
                  <textarea
                    value={storeSettings.storeDescription}
                    onChange={(e) => handleStoreSettingChange('storeDescription', e.target.value)}
                    rows={3}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:ring-2 focus:border-transparent resize-none ${
                      validationErrors.storeDescription
                        ? 'focus:ring-red-500 border-red-300'
                        : 'focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: validationErrors.storeDescription
                        ? '#ef4444'
                        : (resolvedTheme === 'dark' ? '#475569' : '#d1d5db'),
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                    placeholder="Describe your store..."
                  />
                  {validationErrors.storeDescription && (
                    <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      {validationErrors.storeDescription}
                    </p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Store Address
                  </label>
                  <input
                    type="text"
                    value={storeSettings.storeAddress}
                    onChange={(e) => handleStoreSettingChange('storeAddress', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:ring-2 focus:border-transparent ${
                      validationErrors.storeAddress
                        ? 'focus:ring-red-500 border-red-300'
                        : 'focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: validationErrors.storeAddress
                        ? '#ef4444'
                        : (resolvedTheme === 'dark' ? '#475569' : '#d1d5db'),
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                    placeholder="Enter your complete store address"
                  />
                  {validationErrors.storeAddress && (
                    <p className="mt-1 text-sm text-red-500 flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      {validationErrors.storeAddress}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Currency
                  </label>
                  <select
                    value={storeSettings.currency}
                    onChange={(e) => handleStoreSettingChange('currency', e.target.value)}
                    className="w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                  >
                    {SUPPORTED_CURRENCIES.map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.name} ({currency.symbol})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Timezone
                  </label>
                  <select
                    value={storeSettings.timezone}
                    onChange={(e) => handleStoreSettingChange('timezone', e.target.value)}
                    className="w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                  >
                    {SUPPORTED_TIMEZONES.map((timezone) => (
                      <option key={timezone.value} value={timezone.value}>
                        {timezone.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'appearance' && (
            <div
              className="rounded-2xl shadow-lg p-6 border space-y-6"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
              }}
            >
              <div className="flex items-center gap-3 mb-6">
                <Palette className="h-6 w-6 text-purple-500" />
                <h3
                  className="text-xl font-bold"
                  style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                >
                  Appearance Settings
                </h3>
              </div>

              <div>
                <label
                  className="block text-sm font-medium mb-4"
                  style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                >
                  Theme Preference
                </label>
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { value: 'light', label: 'Light', icon: Sun },
                    { value: 'dark', label: 'Dark', icon: Moon },
                    { value: 'system', label: 'System', icon: Monitor }
                  ].map((themeOption) => {
                    const Icon = themeOption.icon
                    const isSelected = userPreferences.theme === themeOption.value

                    return (
                      <button
                        key={themeOption.value}
                        onClick={() => handleUserPreferenceChange('theme', themeOption.value)}
                        className={`p-4 rounded-xl border transition-all duration-200 ${
                          isSelected ? 'shadow-lg' : 'hover:shadow-md'
                        }`}
                        style={{
                          backgroundColor: isSelected
                            ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')
                            : (resolvedTheme === 'dark' ? '#334155' : '#f9fafb'),
                          borderColor: isSelected
                            ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.4)' : 'rgba(34, 197, 94, 0.3)')
                            : (resolvedTheme === 'dark' ? '#475569' : '#d1d5db')
                        }}
                      >
                        <Icon
                          className="h-6 w-6 mx-auto mb-2"
                          style={{
                            color: isSelected
                              ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                              : (resolvedTheme === 'dark' ? '#94a3b8' : '#64748b')
                          }}
                        />
                        <div
                          className="text-sm font-medium text-center"
                          style={{
                            color: isSelected
                              ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                              : (resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937')
                          }}
                        >
                          {themeOption.label}
                        </div>
                      </button>
                    )
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div
              className="rounded-2xl shadow-lg p-6 border space-y-6"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
              }}
            >
              <div className="flex items-center gap-3 mb-6">
                <Bell className="h-6 w-6 text-yellow-500" />
                <h3
                  className="text-xl font-bold"
                  style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                >
                  Notification Settings
                </h3>
              </div>

              <div className="space-y-6">
                <div>
                  <h4
                    className="text-lg font-semibold mb-4"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Notification Channels
                  </h4>
                  <div className="space-y-4">
                    {[
                      { key: 'email', label: 'Email Notifications', icon: Mail, description: 'Receive notifications via email' },
                      { key: 'push', label: 'Push Notifications', icon: Smartphone, description: 'Browser push notifications' },
                      { key: 'sms', label: 'SMS Notifications', icon: MessageSquare, description: 'Text message notifications' }
                    ].map((channel) => {
                      const Icon = channel.icon
                      const isEnabled = userPreferences.notifications[channel.key as keyof typeof userPreferences.notifications]

                      return (
                        <div
                          key={channel.key}
                          className="flex items-center justify-between p-4 rounded-lg border"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                            borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <Icon
                              className="h-5 w-5"
                              style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                            />
                            <div>
                              <div
                                className="font-medium"
                                style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                              >
                                {channel.label}
                              </div>
                              <div
                                className="text-sm"
                                style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                              >
                                {channel.description}
                              </div>
                            </div>
                          </div>
                          <button
                            onClick={() => handleUserPreferenceChange(`notifications.${channel.key}`, !isEnabled)}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
                              isEnabled ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                isEnabled ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <h4
                    className="text-lg font-semibold mb-4"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Alert Types
                  </h4>
                  <div className="space-y-4">
                    {[
                      { key: 'lowStock', label: 'Low Stock Alerts', description: 'Get notified when products are running low' },
                      { key: 'newDebt', label: 'New Debt Notifications', description: 'Alert when new customer debt is recorded' },
                      { key: 'payments', label: 'Payment Notifications', description: 'Notifications for debt payments received' }
                    ].map((alertType) => {
                      const isEnabled = userPreferences.notifications[alertType.key as keyof typeof userPreferences.notifications]

                      return (
                        <div
                          key={alertType.key}
                          className="flex items-center justify-between p-4 rounded-lg border"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                            borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
                          }}
                        >
                          <div>
                            <div
                              className="font-medium"
                              style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                            >
                              {alertType.label}
                            </div>
                            <div
                              className="text-sm"
                              style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                            >
                              {alertType.description}
                            </div>
                          </div>
                          <button
                            onClick={() => handleUserPreferenceChange(`notifications.${alertType.key}`, !isEnabled)}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
                              isEnabled ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                isEnabled ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'user' && (
            <div
              className="rounded-2xl shadow-lg p-6 border space-y-6"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
              }}
            >
              <div className="flex items-center gap-3 mb-6">
                <User className="h-6 w-6 text-green-500" />
                <h3
                  className="text-xl font-bold"
                  style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                >
                  User Preferences
                </h3>
              </div>

              <div className="space-y-6">
                <div>
                  <h4
                    className="text-lg font-semibold mb-4"
                    style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                  >
                    Dashboard Settings
                  </h4>
                  <div className="space-y-4">
                    <div
                      className="flex items-center justify-between p-4 rounded-lg border"
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                        borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
                      }}
                    >
                      <div>
                        <div
                          className="font-medium"
                          style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                        >
                          Auto Refresh Dashboard
                        </div>
                        <div
                          className="text-sm"
                          style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                        >
                          Automatically refresh dashboard data
                        </div>
                      </div>
                      <button
                        onClick={() => handleUserPreferenceChange('dashboard.autoRefresh', !userPreferences.dashboard.autoRefresh)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
                          userPreferences.dashboard.autoRefresh ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            userPreferences.dashboard.autoRefresh ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>

                    <div>
                      <label
                        className="block text-sm font-medium mb-2"
                        style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151' }}
                      >
                        Refresh Interval (minutes)
                      </label>
                      <select
                        value={userPreferences.dashboard.refreshInterval}
                        onChange={(e) => handleUserPreferenceChange('dashboard.refreshInterval', parseInt(e.target.value))}
                        disabled={!userPreferences.dashboard.autoRefresh}
                        className="w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:ring-2 focus:ring-green-500 focus:border-transparent disabled:opacity-50"
                        style={{
                          backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                          borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                          color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                        }}
                      >
                        {REFRESH_INTERVALS.map((interval) => (
                          <option key={interval.value} value={interval.value}>
                            {interval.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div
              className="rounded-2xl shadow-lg p-6 border space-y-6"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
              }}
            >
              <div className="flex items-center gap-3 mb-6">
                <Shield className="h-6 w-6 text-red-500" />
                <h3
                  className="text-xl font-bold"
                  style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                >
                  Security Settings
                </h3>
              </div>

              <div className="space-y-6">
                <div
                  className="p-4 rounded-lg border-l-4 border-yellow-500"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(251, 191, 36, 0.1)' : 'rgba(251, 191, 36, 0.05)',
                    borderColor: '#f59e0b'
                  }}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <span
                      className="font-medium"
                      style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                    >
                      Security Notice
                    </span>
                  </div>
                  <p
                    className="text-sm"
                    style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                  >
                    Advanced security features will be available in future updates. Currently using basic authentication.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    className="p-4 rounded-lg border text-center"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
                    }}
                  >
                    <Lock className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <div
                      className="font-medium mb-1"
                      style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                    >
                      Password Protection
                    </div>
                    <div
                      className="text-sm"
                      style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                    >
                      Account secured with password
                    </div>
                  </div>

                  <div
                    className="p-4 rounded-lg border text-center"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
                    }}
                  >
                    <Key className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                    <div
                      className="font-medium mb-1"
                      style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                    >
                      API Keys
                    </div>
                    <div
                      className="text-sm"
                      style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                    >
                      Secure API access configured
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'system' && (
            <div
              className="rounded-2xl shadow-lg p-6 border space-y-6"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
              }}
            >
              <div className="flex items-center gap-3 mb-6">
                <Database className="h-6 w-6 text-indigo-500" />
                <h3
                  className="text-xl font-bold"
                  style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                >
                  System Configuration
                </h3>
              </div>

              <div className="space-y-6">
                <div
                  className="p-4 rounded-lg border-l-4 border-blue-500"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
                    borderColor: '#3b82f6'
                  }}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="h-5 w-5 text-blue-500" />
                    <span
                      className="font-medium"
                      style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                    >
                      System Information
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span
                        className="font-medium"
                        style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                      >
                        Version:
                      </span>
                      <span
                        className="ml-2"
                        style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                      >
                        v2.0.0
                      </span>
                    </div>
                    <div>
                      <span
                        className="font-medium"
                        style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                      >
                        Environment:
                      </span>
                      <span
                        className="ml-2"
                        style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937' }}
                      >
                        Development
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <button
                    className="flex items-center justify-center gap-2 p-4 rounded-lg border transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'
                    }}
                  >
                    <Download className="h-5 w-5" />
                    Export Data
                  </button>

                  <button
                    className="flex items-center justify-center gap-2 p-4 rounded-lg border transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'
                    }}
                  >
                    <Upload className="h-5 w-5" />
                    Import Data
                  </button>

                  <button
                    className="flex items-center justify-center gap-2 p-4 rounded-lg border transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.05)',
                      borderColor: '#ef4444',
                      color: '#ef4444'
                    }}
                  >
                    <Trash2 className="h-5 w-5" />
                    Clear Cache
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
